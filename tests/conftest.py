import pytest


@pytest.fixture(scope="session")
def nebula_space():
    return "KG125276"


@pytest.fixture(scope="session")
def nebula_eid():
    return "ETDK3BwDxoL9VZUkcBCh4Q"


@pytest.fixture(scope="session")
def neo4j_space():
    return "KG125279"


@pytest.fixture(scope="session")
def neo4j_eid():
    return "Itk3tC0UMVaob_8NU1Guvw"


@pytest.fixture(scope="session")
def nebula_graph_config():
    return {
        "host": "*************",
        "port": 9669,
        "username": "root",
        "password": "root",
    }


@pytest.fixture(scope="session")
def neo4j_graph_config():
    return {
        "host": "*************",
        "port": 20873,
        "username": "neo4j",
        "password": "yunfu2017",
    }
