import json
from typing import Dict

from asgiref.sync import sync_to_async
from docarray import DocumentArray
from yfflow import YfExecutor, requests

from backend.counters import Counter
from backend.graph import get_graph_mapper
from backend.models import Kg, KgOntologies, KgRelationTypes, KgVersion
from backend.utils.db_con_util import close_old_database_connections
from backend.utils.utils import GraphMapperUtil


class CountExecutor(YfExecutor):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.counter = Counter()

    @requests(on="/kg_count")
    async def kg_count(
        self, docs: DocumentArray, **kwargs: Dict[str, dict]
    ) -> DocumentArray:
        """图谱中实体、关系、属性数量统计"""
        for doc in docs:
            kg_id = doc.tags["kg_id"]
            type_ = doc.tags.get("type", "kg")
            version_number = doc.tags.get("version_number", 0)
            self.logger.info(f"{kg_id} 执行图谱实体、关系、属性数量统计任务")
            await sync_to_async(self.process_kg_count)(
                int(kg_id), int(version_number), type_
            )
            self.logger.info(f"{kg_id} 执行图谱实体、关系、属性数量统计任务完成")

    @close_old_database_connections
    def process_kg_count(self, kg_id: int, version_number: int, type_: str):
        self.counter.count(kg_id, version_number, type_)

    @requests(on="/calc_ontologies")
    async def calc_ontologies(
        self, docs: DocumentArray, **kwargs: Dict[str, dict]
    ) -> DocumentArray:
        """图谱中实体、关系、属性数量统计"""
        for doc in docs:
            kg_id = doc.tags["kg_id"]
            self.logger.info(f"{kg_id} 执行本体实体、关系、属性数量统计任务")
            await sync_to_async(self.process_calc_ontologies)(int(kg_id))
            self.logger.info(f"{kg_id} 执行本体实体、关系、属性数量统计任务完成")

    @close_old_database_connections
    def process_calc_ontologies(self, kg_id: int):
        label = GraphMapperUtil.get_label(kg_id)
        kg = Kg.objects.get(id=kg_id)
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        ontologies = graph_mapper.get_ontologies(label)
        KgOntologies.objects.update_or_create(
            defaults={"ontologies": json.dumps(ontologies, ensure_ascii=False)},
            kg_id=kg_id,
        )

    @requests(on="/get_ontologies")
    async def get_ontologies(
        self, docs: DocumentArray, **kwargs: Dict[str, dict]
    ) -> DocumentArray:
        """图谱中实体、关系、属性数量统计"""
        for doc in docs:
            kg_id = doc.tags["kg_id"]
            self.logger.info(f"获取图谱{kg_id}中的本体信息")
            ontologies = await sync_to_async(self.process_get_ontologies)(int(kg_id))
            doc.tags["data"] = ontologies
            self.logger.info(f"获取图谱{kg_id}中的本体信息完成")

    @close_old_database_connections
    def process_get_ontologies(self, kg_id: int):
        label = GraphMapperUtil.get_label(kg_id)
        record = KgOntologies.objects.filter(kg_id=kg_id).first()
        if record:
            return json.loads(record.ontologies)
        kg = Kg.objects.get(id=kg_id)
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        return graph_mapper.get_ontologies(label)

    @requests(on="/calc_relation_types")
    async def count(
        self, docs: DocumentArray, **kwargs: Dict[str, dict]
    ) -> DocumentArray:
        """图谱中实体、关系、属性数量统计"""
        for doc in docs:
            kg_id = doc.tags["kg_id"]
            self.logger.info(f"获取图谱{kg_id}中的本体信息")
            await sync_to_async(self.process_calc_relation_types)(int(kg_id))
            self.logger.info(f"获取图谱{kg_id}中的本体信息完成")

    @close_old_database_connections
    def process_calc_relation_types(self, kg_id: int):
        label = GraphMapperUtil.get_label(kg_id)
        # self.logger.info('开始计算！')
        kg = Kg.objects.get(id=kg_id)
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        relation_types = graph_mapper.get_relation_types(label)
        KgRelationTypes.objects.update_or_create(
            defaults={"relation_types": json.dumps(relation_types, ensure_ascii=False)},
            kg_id=kg_id,
        )
        self.logger.info(f"relation_types: {relation_types}")

    @requests(on="/get_relation_types")
    async def get(
        self, docs: DocumentArray, **kwargs: Dict[str, dict]
    ) -> DocumentArray:
        """图谱中实体、关系、属性数量统计"""
        for doc in docs:
            kg_id = doc.tags["kg_id"]
            eid = doc.tags["eid"]
            self.logger.info(f"{kg_id=}")
            relation_types = await sync_to_async(self.get_relation_types)(
                int(kg_id), eid
            )
            doc.tags["data"] = relation_types
            self.logger.info("统计完成！")

    @close_old_database_connections
    def get_relation_types(self, kg_id: int, eid: str):
        label = GraphMapperUtil.get_label(kg_id)
        self.logger.info("开始统计！")
        kg = Kg.objects.get(id=kg_id)
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        if eid:
            self.logger.info(f"eid is {eid}")
            relation_types = graph_mapper.get_relation_types(label, eid)
        else:
            record = KgRelationTypes.objects.filter(kg_id=kg_id).first()
            if record:
                relation_types = json.loads(record.relation_types)
            else:
                relation_types = graph_mapper.get_relation_types(label)
        self.logger.info(f"relation_types: {relation_types}")
        return relation_types

    @requests(on="/update_center_node")
    async def update_center_node(
        self, docs: DocumentArray, **kwargs: Dict[str, dict]
    ) -> DocumentArray:
        for doc in docs:
            kg_id = doc.tags["kg_id"]
            type_ = doc.tags.get("type")
            version_number = doc.tags.get("version_number", 0)
            self.logger.info(f"{kg_id} {type_} 执行中心节点更新任务")
            await sync_to_async(self.process_center_node)(
                int(kg_id), type_, int(version_number)
            )
            self.logger.info(f"{kg_id} {type_} 执行中心节点更新任务完成")

    @close_old_database_connections
    def process_center_node(self, kg_id: int, type_: str = "", version_number: int = 0):
        show_ontology = False
        if type_ == "ontology":
            show_ontology = True
        space = GraphMapperUtil.get_label(kg_id)
        version = KgVersion.number2str(version_number)
        kg = Kg.objects.get(id=kg_id)
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        kg_version = kg.versions.filter(number=version_number).first()
        if kg_version and kg_version.center_node_eid:
            try:
                node = graph_mapper.get_node_by_eid(space, kg_version.center_node_eid)
                if node:
                    self.logger.info(f"{kg_id} 中心节点已配置，无需更新")
                    return
            except ValueError:
                pass
        nodes = graph_mapper.get_max_relation_node_with_labels(
            space, version=version, show_ontology=show_ontology
        )
        hot_entities = []
        if len(nodes) <= 0:
            nodes = graph_mapper.get_single_node_with_labels(
                space, version=version, show_ontology=show_ontology
            )
        if len(nodes) > 0:
            center_node_eid = nodes[0].props.get("_eid")
            self.logger.info(f"设置图谱{kg_id}的中心节点为{center_node_eid}")
            for node in nodes:
                name = node.props.get("_show_name")
                eid = node.props.get("_eid")
                hot_entity = {"id": eid, "name": name, "eid": eid}
                self.logger.info(f"设置热门实体{hot_entity}")
                hot_entities.append(hot_entity)
            if kg_version:
                kg_version.center_node_eid = center_node_eid
                kg_version.hot_entities = hot_entities
                kg_version.save(update_fields=["center_node_eid", "hot_entities"])
        else:
            self.logger.info(f"{kg_id} 图谱为空或未找到中心节点")
